import { User } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { EmailService } from "src/email/email.service";
import { PrismaService } from "src/prisma/prisma.service";
export declare class AdminService {
    private readonly prisma;
    private readonly emailService;
    constructor(prisma: PrismaService, emailService: EmailService);
    isInviteExists(email: string): Promise<boolean>;
    useInvite(email: string): Promise<{
        name: string | null;
        email: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        locale: import("@prisma/client").$Enums.Locale;
        isUsed: boolean;
    }>;
    getUserInvites(input: User.GetUserInvitesInput, user: CurrentUser): Promise<{
        name: string | null;
        email: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        locale: import("@prisma/client").$Enums.Locale;
        isUsed: boolean;
    }[]>;
    upsertUserInvite(input: User.UpsertUserInviteInput, user: CurrentUser): Promise<{
        name: string | null;
        email: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        locale: import("@prisma/client").$Enums.Locale;
        isUsed: boolean;
    }>;
    deleteUserInvite(input: User.DeleteUserInviteInput, user: CurrentUser): Promise<{
        name: string | null;
        email: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        locale: import("@prisma/client").$Enums.Locale;
        isUsed: boolean;
    }>;
}
