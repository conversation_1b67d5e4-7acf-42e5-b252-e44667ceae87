"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const tag_module_1 = require("./tag/tag.module");
const user_module_1 = require("./user/user.module");
const vote_module_1 = require("./vote/vote.module");
const auth_module_1 = require("./auth/auth.module");
const admin_module_1 = require("./admin/admin.module");
const email_module_1 = require("./email/email.module");
const minio_module_1 = require("./minio/minio.module");
const prisma_module_1 = require("./prisma/prisma.module");
const voting_module_1 = require("./voting/voting.module");
const rating_module_1 = require("./rating/rating.module");
const commune_module_1 = require("./commune/commune.module");
const reactor_module_1 = require("./reactor/reactor.module");
const app_service_1 = require("./app.service");
const app_controller_1 = require("./app.controller");
const config_module_1 = require("./config/config.module");
const sitemap_module_1 = require("./sitemap/sitemap.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            {
                module: config_module_1.ConfigModule,
                global: true,
            },
            {
                module: prisma_module_1.PrismaModule,
                global: true,
            },
            passport_1.PassportModule,
            sitemap_module_1.SitemapModule,
            auth_module_1.AuthModule,
            user_module_1.UserModule,
            commune_module_1.CommuneModule,
            voting_module_1.VotingModule,
            vote_module_1.VoteModule,
            email_module_1.EmailModule,
            minio_module_1.MinioModule,
            auth_module_1.AuthModule,
            reactor_module_1.ReactorModule,
            rating_module_1.RatingModule,
            tag_module_1.TagModule,
            admin_module_1.AdminModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map