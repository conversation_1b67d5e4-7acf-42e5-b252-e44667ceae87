import { Reactor } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { MinioService } from "src/minio/minio.service";
import { PrismaService } from "src/prisma/prisma.service";
export declare class ReactorCommunityService {
    private readonly prisma;
    private readonly minioService;
    private readonly logger;
    constructor(prisma: PrismaService, minioService: MinioService);
    getCommunityIds(): Promise<string[]>;
    getCommunities(input: Reactor.GetCommunitiesInput, user: CurrentUser | null): Promise<{
        description: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        hub: ({
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
            name: {
                value: string;
                locale: import("@prisma/client").$Enums.Locale;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            imageId: string | null;
            headUserId: string;
        }) | null;
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        headUser: {
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
            name: {
                value: string;
                locale: import("@prisma/client").$Enums.Locale;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            email: string;
            referrerId: string | null;
            role: import("@prisma/client").$Enums.UserRole;
            imageId: string | null;
        };
    }[]>;
    createCommunity(input: Reactor.CreateCommunityInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        imageId: string | null;
        hubId: string | null;
        headUserId: string;
    }>;
    updateCommunity(input: Reactor.UpdateCommunityInput, user: CurrentUser): Promise<void>;
    updateCommunityImage(id: string, file: Express.Multer.File, user: CurrentUser): Promise<void>;
    deleteCommunityImage(id: string, user: CurrentUser): Promise<void>;
}
