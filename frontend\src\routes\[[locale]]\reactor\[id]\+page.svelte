<script lang="ts">
  import type { Common } from "@commune/api";
  import type { CommentEntity } from "./comment-tree";

  import { getClient } from "$lib/acrpc";
  import PostCard from "../post-card.svelte";
  import RightMenu from "../right-menu.svelte";
  import Comment from "./comment.svelte";
  import { CommentTree } from "./comment-tree";
  import { LocalizedTextarea } from "$lib/components";
  import CreatePostModal from "$lib/components/create-post-modal.svelte";

  const i18n = {
    en: {
      reactor: "Reactor",
      comments: "Comments",
      commentPlaceholder: "Write your comment...",
      submit: "Submit",
    },
    ru: {
      reactor: "Реактор",
      comments: "Комментарии",
      commentPlaceholder: "Напишите ваш комментарий...",
      submit: "Отправить",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, routeLocale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  let post = $state(data.post);

  const title = $derived(getAppropriateLocalization(post.title));

  let comments = $state<CommentEntity[]>(data.comments);
  const commentTree = $derived(new CommentTree(comments));

  let commentText = $state<Common.Localizations>([]);

  // Edit modal state
  let showEditModal = $state(false);
  let postToEdit = $state<typeof post | undefined>(undefined);

  function handleEditPost(postData: typeof post) {
    postToEdit = postData;
    showEditModal = true;
  }

  function handleCloseEditModal() {
    showEditModal = false;
    postToEdit = undefined;
  }

  function handlePostUpdated() {
    // Refresh the page to show updated content
    window.location.reload();
  }

  async function addComment(id: string) {
    const [comment] = await api.reactor.comment.list.get({ id });

    if (!comment) {
      throw new Error("Comment not found");
    }

    comments = [
      ...comments,
      {
        ...comment,
        isMustBeTop: true,
      },
    ];

    const parentPath = commentTree.getParentPath(comment.path);

    if (parentPath) {
      commentTree.incrementChildrenCount(parentPath);
    }
  }

  async function submitComment() {
    const { id } = await api.reactor.comment.post({
      entityType: "post",
      entityId: post.id,
      body: commentText,
    });

    commentText = [];

    addComment(id);
  }
</script>

<svelte:head>
  <title>{title} — {t.reactor}</title>
</svelte:head>

<!-- Desktop Layout (hidden on mobile) -->
<div class="d-none d-lg-block">
  <div class="row g-4 mt-3">
    <div class="col-3"></div>

    <!-- Main Content (2-9 columns) -->
    <div class="col-6">
      <div class="post-detail">
        <PostCard
          {locale}
          {post}
          {toLocaleHref}
          {getAppropriateLocalization}
          currentUser={data.user}
          onEditPost={handleEditPost}
        />

        <!-- Comments Section -->
        <div class="comments-section mt-4">
          <h4 class="mb-3">{t.comments} ({comments.length})</h4>

          <div class="comments-list">
            {#if commentTree}
              {#each commentTree.getRootComments() as comment (comment.id)}
                <Comment
                  {comment}
                  {locale}
                  {routeLocale}
                  expanded={false}
                  {commentTree}
                  {addComment}
                  {toLocaleHref}
                  {getAppropriateLocalization}
                />
              {/each}
            {/if}

            <!-- {#each comments as comment (comment.id)}
              <Comment {comment} {locale} expanded={false} />
            {/each} -->
          </div>
        </div>

        <!-- Post Comment Form -->
        <div class="post-comment-form mt-4">
          <LocalizedTextarea
            {locale}
            label=""
            placeholder={t.commentPlaceholder}
            rows={3}
            bind:value={commentText}
            languageSelectPosition="bottom"
          >
            <button class="btn btn-success btn-sm" onclick={submitComment}>
              <i class="bi bi-send me-1"></i>
              {t.submit}
            </button>
          </LocalizedTextarea>
        </div>
      </div>
    </div>

    <!-- Right Menu (10-11 columns) -->
    <div class="col-2">
      <RightMenu {locale} {toLocaleHref} />
    </div>
  </div>
</div>

<!-- Mobile Layout -->
<div class="d-lg-none">
  <div class="mobile-post-detail">
    <PostCard
      {locale}
      {post}
      {toLocaleHref}
      {getAppropriateLocalization}
      currentUser={data.user}
      onEditPost={handleEditPost}
    />

    <!-- Comments Section -->
    <div class="comments-section mt-4">
      <h4 class="mb-3">{t.comments} ({comments.length})</h4>

      <div class="comments-list">
        {#if commentTree}
          {#each commentTree.getRootComments() as comment (comment.id)}
            <Comment
              {comment}
              {locale}
              {routeLocale}
              expanded={false}
              {commentTree}
              {addComment}
              {toLocaleHref}
              {getAppropriateLocalization}
            />
          {/each}
        {/if}
      </div>
    </div>

    <!-- Post Comment Form -->
    <div class="post-comment-form mt-4">
      <LocalizedTextarea
        {locale}
        label=""
        placeholder={t.commentPlaceholder}
        rows={3}
        bind:value={commentText}
        languageSelectPosition="bottom"
      >
        <button class="btn btn-success btn-sm" onclick={submitComment}>
          <i class="bi bi-send me-1"></i>
          {t.submit}
        </button>
      </LocalizedTextarea>
    </div>
  </div>
</div>

<!-- Edit Post Modal -->
<CreatePostModal
  show={showEditModal}
  {locale}
  {toLocaleHref}
  onClose={handleCloseEditModal}
  onPostCreated={handlePostUpdated}
  post={postToEdit}
/>

<style lang="scss">
  .post-detail {
    max-width: 100%;
  }

  .mobile-post-detail {
    max-width: 100%;
    padding: 0 0.75rem;
  }

  .comments-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
  }

  /* Mobile-specific styles */
  @media (max-width: 991.98px) {
    .mobile-post-detail {
      padding: 0 1rem;
    }
  }

  @media (max-width: 767.98px) {
    .mobile-post-detail {
      padding: 0 0.5rem;
      margin-top: 0.5rem;
    }

    .comments-section {
      padding: 1rem;
      margin-top: 1rem !important;
    }

    .comments-section h4 {
      font-size: 1.1rem;
      margin-bottom: 1rem !important;
    }

    .post-comment-form {
      margin-top: 1rem !important;
    }

    .post-comment-form .btn {
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
    }

    /* Better spacing for mobile */
    .comments-list {
      margin-bottom: 1rem;
    }
  }

  @media (max-width: 576px) {
    .mobile-post-detail {
      padding: 0 0.25rem;
    }

    .comments-section {
      padding: 0.75rem;
      border-radius: 0.375rem;
    }

    .comments-section h4 {
      font-size: 1rem;
    }

    .post-comment-form .btn {
      width: 100%;
      justify-content: center;
    }
  }

  /* Touch-friendly interactions for mobile */
  @media (hover: none) and (pointer: coarse) {
    .post-comment-form .btn {
      min-height: 44px;
      padding: 0.75rem 1rem;
    }
  }

  /* Tablet-specific adjustments */
  @media (min-width: 768px) and (max-width: 991.98px) {
    .mobile-post-detail {
      padding: 0 1.5rem;
    }

    .comments-section {
      padding: 1.25rem;
    }
  }
</style>
